"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  GraduationCap,
  Save,
  AlertCircle,
  BookOpen,
  Calendar,
  Users,
  Building,
  FileText,
  CheckCircle,
} from "lucide-react";
import { useCreateProgram } from "@/features/api/use-programs";
import { useGetCollegeStreams } from "@/features/api/use-streams";
import { useGetBranchesByStream } from "@/features/api/use-branches";
import { toast } from "sonner";

export default function NewProgramPage() {
  const [user, setUser] = useState<any>(null);
  const [institutionConfig, setInstitutionConfig] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    type: "",
    streamId: "",
    branchId: "",
    duration: 1,
    totalSemesters: 2,
    description: "",
    eligibilityCriteria: "",
    totalSeats: 30,
    department: "",
    admissionStatus: "open",
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const createProgramMutation = useCreateProgram();

  // API hooks for streams and branches
  const { data: streams = [], isLoading: streamsLoading } = useGetCollegeStreams();
  const { data: branches = [], isLoading: branchesLoading } = useGetBranchesByStream(
    formData.streamId || undefined
  );

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadInstitutionConfig();
  }, [router]);

  const loadInstitutionConfig = async () => {
    try {
      const response = await fetch("/api/institution-config");
      if (response.ok) {
        const data = await response.json();
        setInstitutionConfig(data.data);
      }
    } catch (error) {
      console.error("Error loading institution config:", error);
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset branch when stream changes
    if (field === "streamId") {
      setFormData(prev => ({
        ...prev,
        branchId: "",
        [field]: String(value)
      }));
    }

    // Auto-calculate semesters based on duration and type
    if (field === "duration" || field === "type") {
      const duration = field === "duration" ? value as number : formData.duration;
      const type = field === "type" ? value as string : formData.type;

      let semesters = 2; // Default
      if (type === "school") {
        semesters = duration; // 1 academic year for school programs
      } else if (type === "undergraduate" || type === "postgraduate" || type === "diploma") {
        semesters = duration * 2; // 2 semesters per academic year for college programs
      } else if (type === "certificate") {
        semesters = duration * 1; // 1 semester per year for certificates
      }

      setFormData(prev => ({
        ...prev,
        totalSemesters: semesters
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate required fields
      if (!formData.name || !formData.code || !formData.type) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Create program data
      const programData = {
        ...formData,
        type: formData.type as "school" | "undergraduate" | "postgraduate" | "diploma" | "certificate",
        admissionStatus: formData.admissionStatus as "open" | "closed" | "waitlist" | undefined,
        duration: Number(formData.duration),
        totalSemesters: Number(formData.totalSemesters),
        totalSeats: Number(formData.totalSeats),
        // Only include streamId and branchId for college programs
        streamId: formData.type !== "school" && formData.streamId ? formData.streamId : undefined,
        branchId: formData.type !== "school" && formData.branchId ? formData.branchId : undefined,
      };

      await createProgramMutation.mutateAsync(programData);

      toast.success("Academic program created successfully!");
      router.push("/admin/programs");
    } catch (error: any) {
      console.error("Error creating program:", error);
      toast.error(error.message || "Failed to create program");
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Get program types based on institution type
  const getProgramTypes = () => {
    if (!institutionConfig) {
      return [
        { value: "school", label: "School Program" },
        { value: "undergraduate", label: "Undergraduate" },
        { value: "postgraduate", label: "Postgraduate" },
        { value: "diploma", label: "Diploma" },
        { value: "certificate", label: "Certificate" },
      ];
    }

    if (institutionConfig.institutionType === "school") {
      return [
        { value: "school", label: "School Program" },
        { value: "certificate", label: "Certificate Course" },
      ];
    } else {
      return [
        { value: "undergraduate", label: "Undergraduate" },
        { value: "postgraduate", label: "Postgraduate" },
        { value: "diploma", label: "Diploma" },
        { value: "certificate", label: "Certificate" },
      ];
    }
  };

  // Get suggested departments based on institution type
  const getSuggestedDepartments = () => {
    if (!institutionConfig) return [];

    if (institutionConfig.institutionType === "school") {
      return [
        "Primary Education",
        "Secondary Education",
        "Higher Secondary Education",
        "Science Department",
        "Arts Department",
        "Commerce Department",
      ];
    } else {
      return [
        "Computer Science",
        "Electronics",
        "Mechanical Engineering",
        "Civil Engineering",
        "Physics",
        "Chemistry",
        "Mathematics",
        "Biology",
        "Commerce",
        "Management",
        "Arts & Humanities",
      ];
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Academic Program</h1>
            <p className="text-gray-600">Create a new academic program for your institution</p>
          </div>
        </div>

        {/* Institution Type Info */}
        {institutionConfig && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-blue-800">
                <Building className="h-5 w-5" />
                <p>
                  <strong>Institution Type:</strong> {institutionConfig.institutionType === "school" ? "School" : "College"}
                  {" - "}Program options are customized for your institution type.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="name">Program Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="e.g., Bachelor of Technology, Class 10"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="code">Program Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
                    placeholder="e.g., BTECH, CLS10"
                    required
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div>
                  <Label htmlFor="type">Program Type *</Label>
                  <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select program type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getProgramTypes().map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Stream Selection - Only for college programs */}
                {formData.type !== "school" && institutionConfig?.institutionType === "college" && (
                  <div>
                    <Label htmlFor="stream">Academic Stream</Label>
                    <Select
                      value={formData.streamId}
                      onValueChange={(value) => handleInputChange("streamId", value)}
                      disabled={streamsLoading}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={streamsLoading ? "Loading streams..." : "Select stream"} />
                      </SelectTrigger>
                      <SelectContent>
                        {streams.map((stream) => (
                          <SelectItem key={stream.id} value={stream.id}>
                            {stream.name} ({stream.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      Choose the main academic stream (Engineering, Diploma, Pharmacy, etc.)
                    </p>
                  </div>
                )}

                {/* Branch Selection - Only when stream is selected */}
                {formData.streamId && formData.type !== "school" && (
                  <div>
                    <Label htmlFor="branch">Specialization/Branch</Label>
                    <Select
                      value={formData.branchId}
                      onValueChange={(value) => handleInputChange("branchId", value)}
                      disabled={branchesLoading || !formData.streamId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={
                          branchesLoading ? "Loading branches..." :
                          !formData.streamId ? "Select stream first" :
                          "Select specialization"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name} ({branch.shortName})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      Choose the specific specialization within the selected stream
                    </p>
                  </div>
                )}

                <div>
                  <Label htmlFor="duration">Duration (Years) *</Label>
                  <Select value={formData.duration.toString()} onValueChange={(value) => handleInputChange("duration", parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year} Year{year > 1 ? 's' : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="totalSemesters">Total Semesters</Label>
                  <Input
                    id="totalSemesters"
                    type="number"
                    value={formData.totalSemesters}
                    onChange={(e) => handleInputChange("totalSemesters", parseInt(e.target.value) || 0)}
                    min="1"
                    max="24"
                  />
                  <p className="text-xs text-gray-500 mt-1">Auto-calculated based on duration and type</p>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="department">Department</Label>
                  <Select value={formData.department} onValueChange={(value) => handleInputChange("department", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSuggestedDepartments().map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="totalSeats">Total Seats *</Label>
                  <Input
                    id="totalSeats"
                    type="number"
                    value={formData.totalSeats}
                    onChange={(e) => handleInputChange("totalSeats", parseInt(e.target.value) || 0)}
                    min="1"
                    max="1000"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Program Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Program Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="description">Program Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Provide a detailed description of the program, its objectives, and key features..."
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="eligibilityCriteria">Eligibility Criteria</Label>
                <Textarea
                  id="eligibilityCriteria"
                  value={formData.eligibilityCriteria}
                  onChange={(e) => handleInputChange("eligibilityCriteria", e.target.value)}
                  placeholder="Specify the eligibility requirements for admission to this program..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="admissionStatus">Admission Status</Label>
                <Select value={formData.admissionStatus} onValueChange={(value) => handleInputChange("admissionStatus", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select admission status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open">Open for Admissions</SelectItem>
                    <SelectItem value="closed">Admissions Closed</SelectItem>
                    <SelectItem value="waitlist">Waitlist Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Program Summary */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                Program Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Program Name:</span>
                    <span className="text-sm font-medium">{formData.name || "Not specified"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Program Code:</span>
                    <span className="text-sm font-medium">{formData.code || "Not specified"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Type:</span>
                    <span className="text-sm font-medium">
                      {formData.type ? formData.type.charAt(0).toUpperCase() + formData.type.slice(1) : "Not selected"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <span className="text-sm font-medium">{formData.duration} year{formData.duration > 1 ? 's' : ''}</span>
                  </div>
                  {formData.streamId && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Stream:</span>
                      <span className="text-sm font-medium">
                        {streams.find(s => s.id === formData.streamId)?.name || "Selected"}
                      </span>
                    </div>
                  )}
                  {formData.branchId && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Branch:</span>
                      <span className="text-sm font-medium">
                        {branches.find(b => b.id === formData.branchId)?.shortName || "Selected"}
                      </span>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Semesters:</span>
                    <span className="text-sm font-medium">{formData.totalSemesters}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Seats:</span>
                    <span className="text-sm font-medium">{formData.totalSeats}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Department:</span>
                    <span className="text-sm font-medium">{formData.department || "Not specified"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Admission Status:</span>
                    <span className="text-sm font-medium">
                      {formData.admissionStatus === "open" ? "Open" :
                       formData.admissionStatus === "closed" ? "Closed" : "Waitlist"}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !formData.name || !formData.code || !formData.type}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Program
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Important Notes */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <AlertCircle className="h-5 w-5" />
              Important Notes
            </CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-blue-700 space-y-2">
            <ul className="list-disc list-inside space-y-1">
              <li>Program code must be unique across all programs</li>
              <li>For college programs: Stream → Branch → Program hierarchy is supported</li>
              <li>Total semesters are auto-calculated: 2 per academic year for colleges, 1 per year for schools</li>
              <li>Semester-based programs are only available for college institution types</li>
              <li>Stream and branch selection is optional but recommended for better organization</li>
              <li>Once created, you can add academic batches and sections to this program</li>
              <li>You can link fee structures to this program after creation</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}