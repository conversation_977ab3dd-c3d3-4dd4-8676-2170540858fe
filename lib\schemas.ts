import { z } from "zod";

// User Authentication Schemas
export const userRoleSchema = z.enum([
  "super_admin",
  "admin",
  "teacher",
  "student",
  "parent",
  "admission_officer",
  "finance_manager",
  "librarian",
  "transport_manager",
  "hostel_manager"
]);

export const userSchema = z.object({
  id: z.string(),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  role: userRoleSchema,
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  avatar: z.string().optional(),
  isActive: z.boolean().default(true),
  lastLogin: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  role: userRoleSchema.optional(),
});

export const registerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  role: userRoleSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const insertUserSchema = userSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Enhanced user creation schema for admin use
export const createUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters").optional(),
  generatePassword: z.boolean().default(false).optional(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  role: userRoleSchema,
  status: z.enum(["active", "inactive"]).default("active").optional(),

  // Role-specific fields
  department: z.string().optional(),
  employeeId: z.string().optional(),
  subjects: z.array(z.string()).optional(),
  classAssignments: z.array(z.string()).optional(),

  // Student-specific fields
  studentId: z.string().optional(),
  classId: z.string().optional(),
  sectionId: z.string().optional(),
  rollNumber: z.string().optional(),

  // Parent-specific fields
  childrenIds: z.array(z.string()).optional(),

  // Additional fields
  dateOfBirth: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  joiningDate: z.string().optional(),
});

// User update schema
export const updateUserSchema = createUserSchema.partial().extend({
  id: z.string(),
});

// Bulk user creation schema
export const bulkCreateUsersSchema = z.object({
  users: z.array(createUserSchema),
  sendNotifications: z.boolean().default(true),
});

// Password reset schemas
export const resetPasswordSchema = z.object({
  userId: z.string(),
  newPassword: z.string().min(8, "Password must be at least 8 characters").optional(),
  generatePassword: z.boolean().default(false),
  sendNotification: z.boolean().default(true),
});

export const bulkPasswordResetSchema = z.object({
  userIds: z.array(z.string()),
  generatePasswords: z.boolean().default(true),
  sendNotifications: z.boolean().default(true),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Password confirmation is required"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Student Schema
export const studentSchema = z.object({
  id: z.string(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  dateOfBirth: z.string(),
  address: z.string().min(5, "Address must be at least 5 characters"),
  enrollmentDate: z.string(),
  studentId: z.string(),
  grade: z.string(),
  section: z.string(),
  parentName: z.string().min(2, "Parent name must be at least 2 characters"),
  parentPhone: z.string().min(10, "Parent phone must be at least 10 digits"),
  parentEmail: z.string().email("Invalid parent email address"),
  status: z.enum(["active", "inactive", "graduated", "transferred"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertStudentSchema = studentSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Teacher Schema
export const teacherSchema = z.object({
  id: z.string(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  dateOfBirth: z.string(),
  address: z.string().min(5, "Address must be at least 5 characters"),
  hireDate: z.string(),
  employeeId: z.string(),
  department: z.string(),
  subject: z.string(),
  qualification: z.string(),
  experience: z.number().min(0, "Experience cannot be negative"),
  salary: z.number().min(0, "Salary cannot be negative"),
  status: z.enum(["active", "inactive", "on_leave"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertTeacherSchema = teacherSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Class Schema
export const classSchema = z.object({
  id: z.string(),
  name: z.string().min(2, "Class name must be at least 2 characters"),
  grade: z.string(),
  section: z.string(),
  teacherId: z.string(),
  subject: z.string(),
  room: z.string(),
  capacity: z.number().min(1, "Capacity must be at least 1"),
  schedule: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  days: z.array(z.string()),
  status: z.enum(["active", "inactive", "completed"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertClassSchema = classSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Attendance Schema
export const attendanceSchema = z.object({
  id: z.string(),
  studentId: z.string(),
  classId: z.string(),
  date: z.string(),
  status: z.enum(["present", "absent", "late", "excused"]),
  notes: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertAttendanceSchema = attendanceSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Grade Schema
export const gradeSchema = z.object({
  id: z.string(),
  studentId: z.string(),
  classId: z.string(),
  assignmentName: z.string(),
  grade: z.number().min(0).max(100),
  maxGrade: z.number().min(1),
  type: z.enum(["assignment", "quiz", "exam", "project", "participation"]),
  date: z.string(),
  notes: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertGradeSchema = gradeSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Assignment Schema
export const assignmentSchema = z.object({
  id: z.string(),
  classId: z.string(),
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string(),
  dueDate: z.string(),
  maxGrade: z.number().min(1),
  type: z.enum(["assignment", "quiz", "exam", "project"]),
  status: z.enum(["draft", "published", "completed"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const insertAssignmentSchema = assignmentSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Finance Management Schema
export const feeStructureSchema = z.object({
  id: z.string(),
  name: z.string().min(2, "Fee structure name is required"),
  grade: z.string(),
  academicYear: z.string(),
  tuitionFee: z.number().min(0),
  admissionFee: z.number().min(0),
  examFee: z.number().min(0),
  libraryFee: z.number().min(0),
  transportFee: z.number().min(0),
  hostelFee: z.number().min(0),
  otherFees: z.number().min(0).default(0),
  totalFee: z.number().min(0),
  dueDate: z.string(),
  status: z.enum(["active", "inactive"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const feePaymentSchema = z.object({
  id: z.string(),
  studentId: z.string(),
  feeStructureId: z.string(),
  amountPaid: z.number().min(0),
  paymentMethod: z.enum(["cash", "card", "bank_transfer", "online", "cheque"]),
  transactionId: z.string().optional(),
  paymentDate: z.string(),
  receiptNumber: z.string(),
  status: z.enum(["pending", "completed", "failed", "refunded"]),
  notes: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Library Management Schema
export const bookSchema = z.object({
  id: z.string(),
  title: z.string().min(2, "Book title is required"),
  author: z.string().min(2, "Author name is required"),
  isbn: z.string(),
  category: z.string(),
  publisher: z.string(),
  publishedYear: z.number(),
  totalCopies: z.number().min(1),
  availableCopies: z.number().min(0),
  location: z.string(),
  price: z.number().min(0),
  status: z.enum(["available", "damaged", "lost"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const bookIssueSchema = z.object({
  id: z.string(),
  bookId: z.string(),
  studentId: z.string().optional(),
  teacherId: z.string().optional(),
  issueDate: z.string(),
  dueDate: z.string(),
  returnDate: z.string().optional(),
  status: z.enum(["issued", "returned", "overdue", "lost"]),
  fineAmount: z.number().min(0).default(0),
  notes: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Transport Management Schema
export const vehicleSchema = z.object({
  id: z.string(),
  vehicleNumber: z.string(),
  vehicleType: z.enum(["bus", "van", "car"]),
  capacity: z.number().min(1),
  driverName: z.string(),
  driverPhone: z.string(),
  driverLicense: z.string(),
  routeId: z.string(),
  status: z.enum(["active", "maintenance", "inactive"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const transportRouteSchema = z.object({
  id: z.string(),
  routeName: z.string(),
  startPoint: z.string(),
  endPoint: z.string(),
  stops: z.array(z.string()),
  distance: z.number().min(0),
  estimatedTime: z.string(),
  fee: z.number().min(0),
  status: z.enum(["active", "inactive"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Hostel Management Schema
export const hostelSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["boys", "girls", "mixed"]),
  totalRooms: z.number().min(1),
  occupiedRooms: z.number().min(0),
  wardenName: z.string(),
  wardenPhone: z.string(),
  address: z.string(),
  facilities: z.array(z.string()),
  monthlyFee: z.number().min(0),
  status: z.enum(["active", "inactive"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const hostelRoomSchema = z.object({
  id: z.string(),
  hostelId: z.string(),
  roomNumber: z.string(),
  roomType: z.enum(["single", "double", "triple", "dormitory"]),
  capacity: z.number().min(1),
  occupiedBeds: z.number().min(0),
  monthlyRent: z.number().min(0),
  facilities: z.array(z.string()),
  status: z.enum(["available", "occupied", "maintenance"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Examination Management Schema
export const examSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["midterm", "final", "unit_test", "practical", "oral"]),
  grade: z.string(),
  subject: z.string(),
  date: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  totalMarks: z.number().min(1),
  passingMarks: z.number().min(0),
  room: z.string(),
  invigilator: z.string(),
  instructions: z.string().optional(),
  status: z.enum(["scheduled", "ongoing", "completed", "cancelled"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Timetable Schema
export const timetableSchema = z.object({
  id: z.string(),
  classId: z.string(),
  teacherId: z.string(),
  subject: z.string(),
  day: z.enum(["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]),
  startTime: z.string(),
  endTime: z.string(),
  room: z.string(),
  academicYear: z.string(),
  semester: z.string(),
  status: z.enum(["active", "inactive"]),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Export types
export type User = z.infer<typeof userSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;
export type BulkCreateUsers = z.infer<typeof bulkCreateUsersSchema>;
export type UserRole = z.infer<typeof userRoleSchema>;
export type Student = z.infer<typeof studentSchema>;
export type InsertStudent = z.infer<typeof insertStudentSchema>;
export type Teacher = z.infer<typeof teacherSchema>;
export type InsertTeacher = z.infer<typeof insertTeacherSchema>;
export type Class = z.infer<typeof classSchema>;
export type InsertClass = z.infer<typeof insertClassSchema>;
export type Attendance = z.infer<typeof attendanceSchema>;
export type InsertAttendance = z.infer<typeof insertAttendanceSchema>;
export type Grade = z.infer<typeof gradeSchema>;
export type InsertGrade = z.infer<typeof insertGradeSchema>;
export type Assignment = z.infer<typeof assignmentSchema>;
export type InsertAssignment = z.infer<typeof insertAssignmentSchema>;
export type FeeStructure = z.infer<typeof feeStructureSchema>;
export type FeePayment = z.infer<typeof feePaymentSchema>;
export type Book = z.infer<typeof bookSchema>;
export type BookIssue = z.infer<typeof bookIssueSchema>;
export type Vehicle = z.infer<typeof vehicleSchema>;
export type TransportRoute = z.infer<typeof transportRouteSchema>;
export type Hostel = z.infer<typeof hostelSchema>;
export type HostelRoom = z.infer<typeof hostelRoomSchema>;
export type Exam = z.infer<typeof examSchema>;
export type Timetable = z.infer<typeof timetableSchema>;
