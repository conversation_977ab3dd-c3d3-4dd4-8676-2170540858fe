"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { HierarchicalProgramSelector } from "@/components/ui/hierarchical-program-selector";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { BookOpen, Building2, GraduationCap } from "lucide-react";

interface SelectedProgram {
  programId: string;
  programName: string;
  programCode: string;
  streamId?: string;
  streamName?: string;
  branchId?: string;
  branchName?: string;
  type: string;
  duration: number;
  totalSeats: number;
  availableSeats?: number;
}

export default function TestHierarchicalPage() {
  const [selectedProgram, setSelectedProgram] = useState<SelectedProgram | null>(null);

  const handleSelectionChange = (selection: SelectedProgram | null) => {
    setSelectedProgram(selection);
    console.log("Program selection changed:", selection);
  };

  const clearSelection = () => {
    setSelectedProgram(null);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Hierarchical Program Selector Test</h1>
            <p className="text-muted-foreground">
              Test the dynamic hierarchical program/class selection component
            </p>
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Program Selector */}
          <div className="space-y-4">
            <HierarchicalProgramSelector
              onSelectionChange={handleSelectionChange}
              selectedProgramId={selectedProgram?.programId}
              className="w-full"
            />
            
            {selectedProgram && (
              <div className="flex justify-end">
                <Button onClick={clearSelection} variant="outline" size="sm">
                  Clear Selection
                </Button>
              </div>
            )}
          </div>

          {/* Selection Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Selection Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedProgram ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                      {selectedProgram.streamId ? (
                        <Building2 className="h-5 w-5 text-blue-600" />
                      ) : (
                        <GraduationCap className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold">{selectedProgram.programName}</h3>
                      <p className="text-sm text-gray-600">Code: {selectedProgram.programCode}</p>
                    </div>
                  </div>

                  <div className="grid gap-3">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Program ID:</span>
                      <Badge variant="outline">{selectedProgram.programId}</Badge>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Type:</span>
                      <Badge>{selectedProgram.type}</Badge>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Duration:</span>
                      <span className="text-sm">{selectedProgram.duration} years</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Total Seats:</span>
                      <span className="text-sm">{selectedProgram.totalSeats}</span>
                    </div>

                    {selectedProgram.availableSeats !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Available Seats:</span>
                        <span className="text-sm text-green-600">{selectedProgram.availableSeats}</span>
                      </div>
                    )}

                    {selectedProgram.streamId && (
                      <>
                        <hr className="my-2" />
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Hierarchical Information</h4>
                          
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Stream:</span>
                            <span className="text-sm">{selectedProgram.streamName}</span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-sm font-medium">Stream ID:</span>
                            <Badge variant="outline" size="sm">{selectedProgram.streamId}</Badge>
                          </div>

                          {selectedProgram.branchId && (
                            <>
                              <div className="flex justify-between">
                                <span className="text-sm font-medium">Branch:</span>
                                <span className="text-sm">{selectedProgram.branchName}</span>
                              </div>

                              <div className="flex justify-between">
                                <span className="text-sm font-medium">Branch ID:</span>
                                <Badge variant="outline" size="sm">{selectedProgram.branchId}</Badge>
                              </div>
                            </>
                          )}
                        </div>
                      </>
                    )}
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="font-medium text-sm mb-2">JSON Output</h4>
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
                      {JSON.stringify(selectedProgram, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No program selected</p>
                  <p className="text-sm text-gray-500">Select a program from the hierarchical selector to see details</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>For School Institution Type:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Should display "Available Classes" instead of "Available Programs"</li>
                <li>Shows grade-based structure (Class 1, Class 2, etc.)</li>
                <li>Flat structure without hierarchical expansion</li>
              </ul>
              
              <p><strong>For College Institution Type:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Should display "Available Programs" with hierarchical structure</li>
                <li>Stream Categories → Program Types → Branches/Specializations</li>
                <li>Expandable tree structure with proper parent-child relationships</li>
                <li>Shows seat availability and admission status</li>
              </ul>

              <p><strong>Features to Test:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Cascading dropdowns and expansion</li>
                <li>Loading states during data fetching</li>
                <li>Error handling for missing data</li>
                <li>Selection persistence and auto-expansion</li>
                <li>Responsive design on different screen sizes</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
