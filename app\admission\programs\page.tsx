"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  GraduationCap,
  BookOpen,
  Users,
  Calendar,
  IndianRupee,
  Clock,
  Award,
  Target,
  Plus,
  Edit,
  Eye,
  Search,
  Filter,
  Download,
  Star,
  CheckCircle,
  AlertTriangle,
  Music,
  Palette,
  Calculator,
  Globe,
  Microscope,
  Trophy,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

export default function AdmissionPrograms() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProgram, setSelectedProgram] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "admission") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const programStats = {
    totalPrograms: 12,
    activePrograms: 10,
    totalSeats: 1200,
    availableSeats: 345,
    applicationDeadline: "2024-03-15",
  };

  // Dynamic programs loaded from API based on institution configuration
  const [programs, setPrograms] = useState<any[]>([]);
  const [institutionConfig, setInstitutionConfig] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Load institution configuration and programs
  useEffect(() => {
    loadInstitutionData();
  }, []);

  const loadInstitutionData = async () => {
    try {
      setLoading(true);

      // Load institution configuration
      const configResponse = await fetch('/api/institution-config');
      const configData = await configResponse.json();

      if (configData.data) {
        setInstitutionConfig(configData.data);

        // Load programs based on institution type
        const programsResponse = await fetch('/api/academic-programs/hierarchical');
        const programsData = await programsResponse.json();

        if (programsData.data) {
          // Transform API data to match component expectations
          const transformedPrograms = programsData.data.programs.map((program: any) => ({
            id: program.id,
            name: program.name,
            description: program.description || `${program.type.charAt(0).toUpperCase() + program.type.slice(1)} program with comprehensive curriculum`,
            category: program.type === "school" ? "Academic" : program.type.charAt(0).toUpperCase() + program.type.slice(1),
            classes: program.type === "school" ? [`Grade ${program.name.split(' ').pop()}`] : [`${program.duration} year program`],
            duration: `${program.duration} year${program.duration > 1 ? 's' : ''}`,
            totalSeats: program.totalSeats,
            availableSeats: program.availableSeats || 0,
            annualFees: 125000, // This should come from fee structure API
            admissionFees: 25000, // This should come from fee structure API
            eligibility: program.eligibilityCriteria || "As per institution guidelines",
            subjects: ["Core Subjects", "Electives", "Practical Sessions"], // This should come from curriculum API
            features: ["Modern Curriculum", "Experienced Faculty", "Well-equipped Labs", "Library Access"],
            applicationDeadline: "2024-03-15", // This should come from admission settings
            status: program.admissionStatus === "open" ? "Active" : "Closed",
            icon: BookOpen,
            color: program.type === "school" ? "blue" : program.type === "undergraduate" ? "green" : "purple",
            type: program.type,
            code: program.code,
            streamName: program.streamName,
            branchName: program.branchName,
          }));

          setPrograms(transformedPrograms);
        }
      }
    } catch (error) {
      console.error('Error loading institution data:', error);
      toast.error('Failed to load program information');
    } finally {
      setLoading(false);
    }
  };

  // Calculate program statistics
  const programStats = {
    totalPrograms: programs.length,
    activePrograms: programs.filter(p => p.status === "Active").length,
    totalSeats: programs.reduce((sum, p) => sum + p.totalSeats, 0),
    availableSeats: programs.reduce((sum, p) => sum + p.availableSeats, 0),
  };


  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Academic": return "text-blue-600 bg-blue-100";
      case "Science": return "text-green-600 bg-green-100";
      case "Commerce": return "text-purple-600 bg-purple-100";
      case "Arts": return "text-orange-600 bg-orange-100";
      case "Sports": return "text-yellow-600 bg-yellow-100";
      case "International": return "text-indigo-600 bg-indigo-100";
      case "Early Years": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "text-green-600 bg-green-100";
      case "Inactive": return "text-red-600 bg-red-100";
      case "Full": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getIconColor = (color: string) => {
    switch (color) {
      case "blue": return "text-blue-600 bg-blue-100";
      case "green": return "text-green-600 bg-green-100";
      case "purple": return "text-purple-600 bg-purple-100";
      case "orange": return "text-orange-600 bg-orange-100";
      case "yellow": return "text-yellow-600 bg-yellow-100";
      case "pink": return "text-pink-600 bg-pink-100";
      case "indigo": return "text-indigo-600 bg-indigo-100";
      case "red": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTabPrograms = () => {
    let filtered = programs;

    // Filter based on school class levels
    switch (selectedTab) {
      case "primary":
        filtered = programs.filter(prog => {
          const className = prog.name.toLowerCase();
          return className.includes("nursery") || className.includes("lkg") ||
                 className.includes("ukg") || className.includes("grade 1") ||
                 className.includes("grade 2") || className.includes("grade 3") ||
                 className.includes("grade 4") || className.includes("grade 5");
        });
        break;
      case "middle":
        filtered = programs.filter(prog => {
          const className = prog.name.toLowerCase();
          return className.includes("grade 6") || className.includes("grade 7") ||
                 className.includes("grade 8");
        });
        break;
      case "secondary":
        filtered = programs.filter(prog => {
          const className = prog.name.toLowerCase();
          return className.includes("grade 9") || className.includes("grade 10");
        });
        break;
      case "senior":
        filtered = programs.filter(prog => {
          const className = prog.name.toLowerCase();
          return className.includes("grade 11") || className.includes("grade 12");
        });
        break;
      default:
        filtered = programs; // Show all classes
    }

    // Apply search filter
    return filtered.filter(prog =>
      prog.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prog.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prog.code && prog.code.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const calculateAvailabilityPercentage = (available: number, total: number) => {
    return ((available / total) * 100).toFixed(1);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Academic Classes
            </h1>
            <p className="text-gray-600">
              Explore available classes and admission options for your child
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Programs
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Program
            </Button>
          </div>
        </div>

        {/* Program Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {programStats.totalPrograms}
                  </div>
                  <p className="text-sm text-gray-600">Total Programs</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {programStats.activePrograms}
                  </div>
                  <p className="text-sm text-gray-600">Active Programs</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {programStats.totalSeats}
                  </div>
                  <p className="text-sm text-gray-600">Total Seats</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {programStats.availableSeats}
                  </div>
                  <p className="text-sm text-gray-600">Available Seats</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-lg font-bold text-red-600">
                    Mar 15
                  </div>
                  <p className="text-sm text-gray-600">Application Deadline</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Programs List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "all", label: "All Classes" },
                    { key: "primary", label: "Primary (Nursery-5)" },
                    { key: "middle", label: "Middle (6-8)" },
                    { key: "secondary", label: "Secondary (9-10)" },
                    { key: "senior", label: "Senior (11-12)" },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search programs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p className="mt-2 text-gray-600">Loading programs...</p>
                  </div>
                </div>
              ) : programs.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No programs available</h3>
                  <p className="text-gray-600">
                    {institutionConfig ?
                      `No programs are currently configured for ${institutionConfig.institutionType} admission.` :
                      "Institution configuration is required to display programs."
                    }
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {getTabPrograms().map((program) => {
                  const IconComponent = program.icon;
                  const availabilityPercentage = calculateAvailabilityPercentage(program.availableSeats, program.totalSeats);
                  
                  return (
                    <div
                      key={program.id}
                      onClick={() => setSelectedProgram(program)}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                        selectedProgram?.id === program.id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-start space-x-4">
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getIconColor(program.color)}`}>
                            <IconComponent className="h-6 w-6" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-1">{program.name}</h4>
                            <p className="text-sm text-gray-600 mb-2">{program.description}</p>
                            <div className="flex items-center space-x-3">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(program.category)}`}>
                                {program.category}
                              </span>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(program.status)}`}>
                                {program.status}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">₹{(program.annualFees / 1000).toFixed(0)}K</div>
                          <div className="text-sm text-gray-500">Annual Fees</div>
                        </div>
                      </div>

                      <div className="grid gap-3 md:grid-cols-3 text-sm mb-3">
                        <div className="flex items-center text-gray-600">
                          <Users className="h-4 w-4 mr-2" />
                          {program.availableSeats}/{program.totalSeats} seats
                        </div>
                        <div className="flex items-center text-gray-600">
                          <Clock className="h-4 w-4 mr-2" />
                          {program.duration}
                        </div>
                        <div className="flex items-center text-gray-600">
                          <Calendar className="h-4 w-4 mr-2" />
                          Due: {program.applicationDeadline}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                          <div 
                            className={`h-2 rounded-full ${
                              parseFloat(availabilityPercentage) > 50 ? 'bg-green-500' :
                              parseFloat(availabilityPercentage) > 25 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${availabilityPercentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-600">
                          {availabilityPercentage}% available
                        </span>
                      </div>
                    </div>
                  );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Program Details */}
          <div className="space-y-6">
            {selectedProgram ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{selectedProgram.name}</span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Program Overview */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Program Overview</h4>
                      <p className="text-sm text-gray-600">{selectedProgram.description}</p>
                    </div>

                    {/* Key Details */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Classes:</span>
                          <span className="font-medium">{selectedProgram.classes.join(", ")}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Duration:</span>
                          <span className="font-medium">{selectedProgram.duration}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Seats:</span>
                          <span className="font-medium">{selectedProgram.totalSeats}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Available:</span>
                          <span className="font-medium text-green-600">{selectedProgram.availableSeats}</span>
                        </div>
                      </div>
                    </div>

                    {/* Fees Structure */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Fees Structure</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Annual Fees:</span>
                          <span className="font-medium">₹{selectedProgram.annualFees.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Admission Fees:</span>
                          <span className="font-medium">₹{selectedProgram.admissionFees.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Eligibility */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Eligibility Criteria</h4>
                      <p className="text-sm text-gray-600">{selectedProgram.eligibility}</p>
                    </div>

                    {/* Subjects */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Subjects</h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedProgram.subjects.map((subject: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {subject}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Features */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
                      <div className="space-y-1">
                        {selectedProgram.features.map((feature: string, index: number) => (
                          <div key={index} className="flex items-center text-sm text-gray-600">
                            <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Application Deadline */}
                    <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-center text-orange-800">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">
                          Application Deadline: {selectedProgram.applicationDeadline}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Program</h3>
                  <p className="text-gray-600">Choose a program from the list to view details</p>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Program
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="h-4 w-4 mr-2" />
                    Manage Seat Allocation
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <IndianRupee className="h-4 w-4 mr-2" />
                    Update Fee Structure
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                    Set Application Deadlines
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
