import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";

// Types for hierarchical program data
export interface HierarchicalAcademicStream {
  id: string;
  name: string;
  code: string;
  category: string;
  description?: string;
  duration: number;
  department?: string;
  branches?: HierarchicalAcademicBranch[];
}

export interface HierarchicalAcademicBranch {
  id: string;
  streamId: string;
  name: string;
  code: string;
  shortName: string;
  description?: string;
  duration: number;
  totalSeats: number;
  programs?: HierarchicalAcademicProgram[];
}

export interface HierarchicalAcademicProgram {
  id: string;
  name: string;
  code: string;
  type: string;
  streamId?: string;
  branchId?: string;
  duration: number;
  totalSemesters?: number;
  department?: string;
  admissionStatus: "open" | "closed" | "waitlist";
  totalSeats: number;
  availableSeats?: number;
}

export interface HierarchicalProgramData {
  streams: HierarchicalAcademicStream[];
  programs: HierarchicalAcademicProgram[];
}

// Hook to fetch hierarchical program data
export const useGetHierarchicalPrograms = () => {
  return useQuery({
    queryKey: ["hierarchical-programs"],
    queryFn: async () => {
      const response = await client.api["academic-programs"]["hierarchical"].$get();

      if (!response.ok) {
        throw new Error("Failed to fetch hierarchical program data");
      }

      const data = await response.json();
      return data.data as HierarchicalProgramData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Helper function to find a program by ID in hierarchical data
export const findProgramInHierarchy = (
  data: HierarchicalProgramData,
  programId: string
): {
  program: HierarchicalAcademicProgram;
  stream?: HierarchicalAcademicStream;
  branch?: HierarchicalAcademicBranch;
} | null => {
  // Check flat programs first (for schools)
  const flatProgram = data.programs.find(p => p.id === programId);
  if (flatProgram) {
    return { program: flatProgram };
  }

  // Check hierarchical programs (for colleges)
  for (const stream of data.streams) {
    if (stream.branches) {
      for (const branch of stream.branches) {
        if (branch.programs) {
          const program = branch.programs.find(p => p.id === programId);
          if (program) {
            return { program, stream, branch };
          }
        }
      }
    }
  }

  return null;
};

// Helper function to get all programs as a flat list
export const getAllProgramsFlat = (data: HierarchicalProgramData): HierarchicalAcademicProgram[] => {
  const allPrograms: HierarchicalAcademicProgram[] = [...data.programs];

  // Add programs from hierarchical structure
  for (const stream of data.streams) {
    if (stream.branches) {
      for (const branch of stream.branches) {
        if (branch.programs) {
          allPrograms.push(...branch.programs);
        }
      }
    }
  }

  return allPrograms;
};

// Helper function to get programs by stream
export const getProgramsByStream = (
  data: HierarchicalProgramData,
  streamId: string
): HierarchicalAcademicProgram[] => {
  const stream = data.streams.find(s => s.id === streamId);
  if (!stream || !stream.branches) return [];

  const programs: HierarchicalAcademicProgram[] = [];
  for (const branch of stream.branches) {
    if (branch.programs) {
      programs.push(...branch.programs);
    }
  }

  return programs;
};

// Helper function to get programs by branch
export const getProgramsByBranch = (
  data: HierarchicalProgramData,
  branchId: string
): HierarchicalAcademicProgram[] => {
  for (const stream of data.streams) {
    if (stream.branches) {
      const branch = stream.branches.find(b => b.id === branchId);
      if (branch && branch.programs) {
        return branch.programs;
      }
    }
  }

  return [];
};

// Helper function to get stream categories
export const getStreamCategories = (data: HierarchicalProgramData): string[] => {
  const categories = new Set<string>();
  for (const stream of data.streams) {
    categories.add(stream.category);
  }
  return Array.from(categories).sort();
};

// Helper function to get streams by category
export const getStreamsByCategory = (
  data: HierarchicalProgramData,
  category: string
): HierarchicalAcademicStream[] => {
  return data.streams.filter(stream => stream.category === category);
};

// Helper function to calculate total available seats
export const calculateTotalAvailableSeats = (data: HierarchicalProgramData): number => {
  const allPrograms = getAllProgramsFlat(data);
  return allPrograms.reduce((total, program) => {
    return total + (program.availableSeats || program.totalSeats);
  }, 0);
};

// Helper function to get admission status summary
export const getAdmissionStatusSummary = (data: HierarchicalProgramData) => {
  const allPrograms = getAllProgramsFlat(data);
  const summary = {
    open: 0,
    closed: 0,
    waitlist: 0,
    total: allPrograms.length,
  };

  for (const program of allPrograms) {
    summary[program.admissionStatus]++;
  }

  return summary;
};
